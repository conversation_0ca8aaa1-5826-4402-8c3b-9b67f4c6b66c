#!/usr/bin/env python3
"""
配置文件 - 桌面监控器设置
"""

# OCR配置
OCR_CONFIG = {
    # Tesseract路径配置（根据系统调整）
    'tesseract_cmd': {
        'windows': r'C:\Program Files\Tesseract-OCR\tesseract.exe',
        'linux': '/usr/bin/tesseract',
        'macos': '/opt/homebrew/bin/tesseract',  # Homebrew安装路径
        'macos_alt': '/usr/local/bin/tesseract'  # 备用路径
    },
    
    # OCR语言设置
    'languages': 'chi_sim+eng',  # 中文简体 + 英文
    
    # OCR参数
    'custom_config': r'--oem 3 --psm 6'
}

# 监控设置
MONITOR_CONFIG = {
    # 监控间隔（秒）
    'interval': 3,
    
    # 监控区域（None表示全屏）
    # 格式：(x, y, width, height)
    'region': None,
    
    # 已处理请求ID的存储文件
    'processed_file': 'processed_requests.json'
}

# Request ID识别模式
REQUEST_ID_PATTERNS = [
    r'req_[a-zA-Z0-9]{8,}',
    r'request_[a-zA-Z0-9]{8,}',
    r'REQ[_-][a-zA-Z0-9]{8,}',
    r'REQUEST[_-][a-zA-Z0-9]{8,}',
    r'[Rr]eq[_-]?\d{8,}',
    r'ID[_-]?\d{8,}',
    # 可以根据实际情况添加更多模式
]

# 输入框识别关键词
INPUT_BOX_KEYWORDS = [
    'Ask or instruct Augment Agent',
    'Augment Agent',
    'Ask or instruct',
    'instruct',
    'Agent'
]

# 自动输入的文字
AUTO_INPUT_TEXT = "继续"

# PyAutoGUI设置
PYAUTOGUI_CONFIG = {
    'failsafe': True,  # 启用安全模式
    'pause': 0.5,      # 操作间隔
    'confidence': 0.8  # 图像识别置信度
}
