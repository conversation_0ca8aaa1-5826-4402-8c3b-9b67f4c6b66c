#!/usr/bin/env python3
"""
桌面监控脚本 - 使用pyautogui识别桌面文字并自动处理request_id
"""

import pyautogui
import cv2
import numpy as np
import time
import json
import os
from datetime import datetime
from PIL import Image
import hashlib

class DesktopMonitor:
    def __init__(self, processed_file="processed_requests.json", monitor_index=None):
        """
        初始化桌面监控器

        Args:
            processed_file: 存储已处理request_id的文件路径
            monitor_index: 指定监控的显示器序号，None表示监控所有显示器
        """
        self.processed_file = processed_file
        self.processed_requests = self.load_processed_requests()
        self.monitor_index = monitor_index

        # 设置pyautogui安全措施
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5

        # 获取显示器信息
        self.monitors = self.get_all_monitors()
        self.print_monitor_info()

        # 配置tesseract路径（如果需要）
        # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'  # Windows
        # pytesseract.pytesseract.tesseract_cmd = '/usr/bin/tesseract'  # Linux
        # pytesseract.pytesseract.tesseract_cmd = '/opt/homebrew/bin/tesseract'  # macOS with Homebrew
    
    def load_processed_requests(self):
        """加载已处理的request_id列表"""
        if os.path.exists(self.processed_file):
            try:
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
            except (json.JSONDecodeError, FileNotFoundError):
                return set()
        return set()
    
    def save_processed_requests(self):
        """保存已处理的request_id列表"""
        with open(self.processed_file, 'w', encoding='utf-8') as f:
            json.dump(list(self.processed_requests), f, ensure_ascii=False, indent=2)
    
    def get_all_monitors(self):
        """
        获取所有显示器信息

        Returns:
            显示器信息列表
        """
        try:
            import screeninfo
            monitors = screeninfo.get_monitors()
            monitor_info = []
            for i, monitor in enumerate(monitors):
                info = {
                    'index': i,
                    'x': monitor.x,
                    'y': monitor.y,
                    'width': monitor.width,
                    'height': monitor.height,
                    'is_primary': monitor.is_primary
                }
                monitor_info.append(info)
            return monitor_info
        except ImportError:
            print("screeninfo包未安装，使用pyautogui获取屏幕信息")
            # 备用方案：使用pyautogui
            width, height = pyautogui.size()
            return [{'index': 0, 'x': 0, 'y': 0, 'width': width, 'height': height, 'is_primary': True}]

    def print_monitor_info(self):
        """打印显示器信息"""
        print(f"检测到 {len(self.monitors)} 个显示器:")
        for monitor in self.monitors:
            primary_str = " (主显示器)" if monitor['is_primary'] else ""
            print(f"  显示器 {monitor['index']}: {monitor['width']}x{monitor['height']} "
                  f"位置({monitor['x']}, {monitor['y']}){primary_str}")

        if self.monitor_index is not None:
            if self.monitor_index < len(self.monitors):
                monitor = self.monitors[self.monitor_index]
                print(f"将监控显示器 {self.monitor_index}: {monitor['width']}x{monitor['height']}")
            else:
                print(f"警告: 指定的显示器序号 {self.monitor_index} 不存在，将监控所有显示器")
                self.monitor_index = None
        else:
            print("将监控所有显示器")

    def capture_screen(self, region=None, monitor_index=None):
        """
        截取屏幕（支持多显示器）

        Args:
            region: 截取区域 (x, y, width, height)，None表示全屏
            monitor_index: 显示器索引，None表示主显示器

        Returns:
            PIL Image对象
        """
        try:
            if monitor_index is not None and monitor_index < len(self.monitors):
                # 获取指定显示器信息
                monitor = self.monitors[monitor_index]

                if region is None:
                    # 截取整个指定显示器
                    region = (monitor['x'], monitor['y'], monitor['width'], monitor['height'])
                else:
                    # 调整region到指定显示器的坐标系，确保在显示器范围内
                    x = max(0, min(region[0], monitor['width'] - 1))
                    y = max(0, min(region[1], monitor['height'] - 1))
                    w = min(region[2], monitor['width'] - x)
                    h = min(region[3], monitor['height'] - y)
                    region = (monitor['x'] + x, monitor['y'] + y, w, h)

                print(f"截取区域: {region}")

            # 验证region是否有效
            if region:
                x, y, w, h = region
                # 确保区域有效
                if w > 0 and h > 0:
                    # 获取屏幕总尺寸
                    screen_width, screen_height = pyautogui.size()

                    # 确保区域在屏幕范围内
                    if (x >= 0 and y >= 0 and
                        x + w <= screen_width and y + h <= screen_height):
                        screenshot = pyautogui.screenshot(region=region)
                    else:
                        # print(f"警告: 区域 {region} 超出屏幕范围，使用全屏截取")
                        screenshot = pyautogui.screenshot()
                else:
                    print(f"警告: 无效区域 {region}，使用全屏截取")
                    screenshot = pyautogui.screenshot()
            else:
                # 截取所有显示器（虚拟屏幕）
                screenshot = pyautogui.screenshot()

            return screenshot

        except Exception as e:
            print(f"屏幕截取失败: {e}")
            print(f"错误详情: 尝试截取区域 {region if 'region' in locals() else 'None'}")
            # 备用方案：截取主显示器
            try:
                return pyautogui.screenshot()
            except Exception as e2:
                print(f"备用截取也失败: {e2}")
                # 最后的备用方案：创建一个空白图像
                from PIL import Image
                return Image.new('RGB', (800, 600), color='white')
    
    def preprocess_image(self, image):
        """
        预处理图像以提高OCR识别率
        
        Args:
            image: PIL Image对象
            
        Returns:
            处理后的numpy数组
        """
        # 转换为numpy数组
        img_array = np.array(image)
        
        # 转换为灰度图
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        
        # 应用高斯模糊减少噪声
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 应用阈值处理
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return thresh
    
    def extract_text_from_image(self, image):
        """
        从图像中提取文字
        
        Args:
            image: PIL Image对象
            
        Returns:
            识别出的文字字符串
        """
        try:
            # 预处理图像
            processed_img = self.preprocess_image(image)
            
            # 使用tesseract进行OCR识别
            # 配置OCR参数：中英文混合识别
            custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
            text = pytesseract.image_to_string(processed_img, config=custom_config)
            print(f"识别到文字:{text}")
            
            return text.strip()
        except Exception as e:
            print(f"OCR识别错误: {e}")
            return ""
    
    def find_request_ids(self, text):
        """
        从文字中查找request_id
        
        Args:
            text: 要搜索的文字
            
        Returns:
            找到的request_id列表
        """
        # 定义request_id的正则表达式模式
        # 假设request_id格式为：req_xxxxxxxx 或 request_xxxxxxxx 或类似格式
        patterns = [
            r'req_[a-zA-Z0-9]{8,}',
            r'request_[a-zA-Z0-9]{8,}',
            r'REQ[_-][a-zA-Z0-9]{8,}',
            r'REQUEST[_-][a-zA-Z0-9]{8,}',
            # 可以根据实际情况添加更多模式
        ]
        
        request_ids = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            request_ids.extend(matches)
        
        return list(set(request_ids))  # 去重
    
    def find_augment_input_box(self):
        """
        查找Augment Agent的输入框

        Returns:
            输入框的位置坐标 (x, y) 或 None
        """
        try:
            # 截取当前屏幕
            screenshot = self.capture_screen()
            img_array = np.array(screenshot)

            # 方法1: 通过OCR查找占位符文字
            text = self.extract_text_from_image(screenshot)

            if "Ask or instruct Augment Agent" in text or "Augment Agent" in text or "Ask or instruct" in text:
                print("检测到Augment Agent输入框文字")

                # 尝试通过OCR定位文字位置
                try:
                    processed_img = self.preprocess_image(screenshot)
                    data = pytesseract.image_to_data(processed_img, output_type=pytesseract.Output.DICT)

                    # 查找包含关键词的文字块
                    keywords = ['Ask', 'instruct', 'Augment', 'Agent']
                    for i, word in enumerate(data['text']):
                        if any(keyword.lower() in word.lower() for keyword in keywords):
                            x = data['left'][i]
                            y = data['top'][i]
                            w = data['width'][i]
                            h = data['height'][i]

                            # 输入框位置通常在占位符文字的中心
                            input_x = x + w // 2
                            input_y = y + h // 2

                            print(f"通过OCR定位到输入框: ({input_x}, {input_y})")
                            return (input_x, input_y)

                except Exception as e:
                    print(f"OCR定位失败: {e}")

            # 方法2: 通过视觉特征查找输入框（圆角矩形边框）
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

            # 使用边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            screen_width, screen_height = pyautogui.size()

            # 查找可能的输入框轮廓
            for contour in contours:
                # 计算轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)

                # 输入框的特征：
                # 1. 宽度较大（至少屏幕宽度的30%）
                # 2. 高度适中（20-80像素）
                # 3. 位置在屏幕下半部分
                if (w > screen_width * 0.3 and
                    20 < h < 80 and
                    y > screen_height * 0.5):

                    input_x = x + w // 2
                    input_y = y + h // 2

                    print(f"通过轮廓检测到可能的输入框: ({input_x}, {input_y})")
                    return (input_x, input_y)

            # 方法3: 备用方案 - 根据界面布局估算位置
            # Augment Agent界面通常输入框在底部
            print("使用备用定位方案")
            return (screen_width // 2, int(screen_height * 0.85))

        except Exception as e:
            print(f"查找输入框时出错: {e}")
            return None

    def find_input_box_and_type(self, text="继续"):
        """
        查找输入框并输入文字

        Args:
            text: 要输入的文字
        """
        try:
            # 首先尝试找到Augment Agent的输入框
            input_position = self.find_augment_input_box()

            if input_position:
                x, y = input_position
                print(f"找到输入框位置: ({x}, {y})")

                # 点击输入框
                pyautogui.click(x, y)

                # 等待输入框获得焦点
                time.sleep(0.5)

                # 清空输入框（以防有残留文字）
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.1)

                # 输入文字
                pyautogui.typewrite(text, interval=0.05)

                # 等待一下再按回车
                time.sleep(0.3)

                # 按回车确认
                pyautogui.press('enter')

                print(f"已输入文字: {text}")
                return True
            else:
                print("未找到Augment Agent输入框")
                return False

        except Exception as e:
            print(f"输入文字时出错: {e}")
            return False
    
    def monitor_desktop(self, interval=5, region=None):
        """
        监控桌面并处理新的request_id

        Args:
            interval: 监控间隔（秒）
            region: 监控区域 (x, y, width, height)，None表示全屏
        """
        print("开始监控桌面...")
        print(f"监控间隔: {interval}秒")
        print(f"已处理的request_id数量: {len(self.processed_requests)}")

        if self.monitor_index is not None:
            monitor = self.monitors[self.monitor_index]
            print(f"监控显示器: {self.monitor_index} ({monitor['width']}x{monitor['height']})")
        else:
            print("监控所有显示器")

        print("按Ctrl+C停止监控")

        try:
            while True:
                # 截取屏幕（使用指定的显示器）
                screenshot = self.capture_screen(region, self.monitor_index)
                
                # 提取文字
                text = self.extract_text_from_image(screenshot)
                
                if text:
                    # 查找request_id
                    request_ids = self.find_request_ids(text)
                    
                    # 检查是否有新的request_id
                    new_request_ids = [rid for rid in request_ids if rid not in self.processed_requests]
                    
                    if new_request_ids:
                        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 发现新的request_id:")
                        for rid in new_request_ids:
                            print(f"  - {rid}")
                        
                        # 在输入框输入"继续"
                        if self.find_input_box_and_type("继续"):
                            # 标记这些request_id为已处理
                            self.processed_requests.update(new_request_ids)
                            self.save_processed_requests()
                            print("已处理新的request_id")
                        else:
                            print("输入失败，将在下次循环重试")
                
                # 等待下次检查
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n监控已停止")
        except Exception as e:
            print(f"监控过程中出错: {e}")

def main():
    """主函数"""
    print("=== Augment Agent 桌面监控器 ===")
    print("功能：自动识别桌面上的request_id并在输入框输入'继续'")
    print("注意：请确保已安装tesseract OCR引擎")
    print()

    try:
        # 首先创建一个临时实例来获取显示器信息
        temp_monitor = DesktopMonitor()

        # 如果有多个显示器，让用户选择
        monitor_index = None
        if len(temp_monitor.monitors) > 1:
            print("\n请选择要监控的显示器:")
            print("0: 监控所有显示器")
            for i, monitor_info in enumerate(temp_monitor.monitors):
                primary_str = " (主显示器)" if monitor_info['is_primary'] else ""
                print(f"{i+1}: 显示器 {i} - {monitor_info['width']}x{monitor_info['height']}{primary_str}")

            while True:
                try:
                    choice = input("\n请输入选择 (0-%d): " % len(temp_monitor.monitors))
                    choice = int(choice)
                    if choice == 0:
                        monitor_index = None
                        break
                    elif 1 <= choice <= len(temp_monitor.monitors):
                        monitor_index = choice - 1
                        break
                    else:
                        print("无效选择，请重新输入")
                except ValueError:
                    print("请输入数字")
                except KeyboardInterrupt:
                    print("\n程序已取消")
                    return

        # 创建监控器实例
        monitor = DesktopMonitor(monitor_index=monitor_index)

        # 测试OCR功能
        print("\n正在测试OCR功能...")
        screenshot = monitor.capture_screen(monitor_index=monitor_index)
        text = monitor.extract_text_from_image(screenshot)
        if text:
            print("✓ OCR功能正常")
        else:
            print("⚠ OCR可能需要配置，请检查tesseract安装")

        print()
        print("开始监控...")
        print("提示：")
        print("- 将鼠标移到屏幕左上角可紧急停止")
        print("- 按Ctrl+C也可停止监控")
        print("- 监控间隔：3秒")
        print()

        # 开始监控
        monitor.monitor_desktop(interval=3)

    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"\n程序出错: {e}")
        print("请检查依赖是否正确安装：")
        print("pip install pyautogui pytesseract opencv-python pillow")

if __name__ == "__main__":
    main()
