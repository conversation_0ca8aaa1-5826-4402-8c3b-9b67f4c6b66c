#!/usr/bin/env python3
"""
多显示器使用示例
演示如何在多显示器环境中使用桌面监控器
"""

from desktop_monitor import DesktopMonitor
import time

def example_basic_usage():
    """基本使用示例 - 监控所有显示器"""
    print("=== 基本使用示例 ===")
    
    # 创建监控器实例（监控所有显示器）
    monitor = DesktopMonitor()
    
    # 开始监控
    monitor.monitor_desktop(interval=5)

def example_specific_monitor():
    """指定显示器示例"""
    print("=== 指定显示器示例 ===")
    
    # 创建监控器实例，指定监控显示器0
    monitor = DesktopMonitor(monitor_index=0)
    
    # 开始监控
    monitor.monitor_desktop(interval=3)

def example_monitor_selection():
    """交互式显示器选择示例"""
    print("=== 交互式显示器选择示例 ===")
    
    # 首先获取显示器信息
    temp_monitor = DesktopMonitor()
    
    print("可用的显示器:")
    for i, monitor_info in enumerate(temp_monitor.monitors):
        primary_str = " (主显示器)" if monitor_info['is_primary'] else ""
        print(f"  显示器 {i}: {monitor_info['width']}x{monitor_info['height']} "
              f"位置({monitor_info['x']}, {monitor_info['y']}){primary_str}")
    
    # 让用户选择
    if len(temp_monitor.monitors) > 1:
        try:
            choice = int(input(f"\n请选择要监控的显示器 (0-{len(temp_monitor.monitors)-1}): "))
            if 0 <= choice < len(temp_monitor.monitors):
                monitor = DesktopMonitor(monitor_index=choice)
                monitor.monitor_desktop(interval=3)
            else:
                print("无效的显示器序号")
        except ValueError:
            print("请输入有效的数字")
    else:
        print("只有一个显示器，开始监控...")
        monitor = DesktopMonitor(monitor_index=0)
        monitor.monitor_desktop(interval=3)

def example_region_monitoring():
    """区域监控示例"""
    print("=== 区域监控示例 ===")
    
    # 创建监控器实例
    monitor = DesktopMonitor(monitor_index=0)  # 监控显示器0
    
    # 定义监控区域 (x, y, width, height)
    # 例如：监控显示器左上角 800x600 的区域
    region = (0, 0, 800, 600)
    
    print(f"监控区域: {region}")
    
    # 开始监控指定区域
    monitor.monitor_desktop(interval=3, region=region)

def example_test_screenshot():
    """测试截图功能"""
    print("=== 测试截图功能 ===")
    
    monitor = DesktopMonitor()
    
    print("测试截图功能...")
    
    # 测试截取所有显示器
    print("1. 截取所有显示器...")
    screenshot_all = monitor.capture_screen()
    print(f"   全屏截图尺寸: {screenshot_all.size}")
    
    # 测试截取每个显示器
    for i, monitor_info in enumerate(monitor.monitors):
        print(f"2. 截取显示器 {i}...")
        screenshot = monitor.capture_screen(monitor_index=i)
        print(f"   显示器 {i} 截图尺寸: {screenshot.size}")
        
        # 可以保存截图用于调试
        # screenshot.save(f"monitor_{i}_screenshot.png")
    
    print("截图测试完成")

def main():
    """主函数 - 提供选择菜单"""
    print("=== 多显示器桌面监控器示例 ===")
    print()
    print("请选择示例:")
    print("1. 基本使用 (监控所有显示器)")
    print("2. 指定显示器监控")
    print("3. 交互式显示器选择")
    print("4. 区域监控示例")
    print("5. 测试截图功能")
    print("0. 退出")
    
    try:
        choice = input("\n请输入选择 (0-5): ")
        
        if choice == "1":
            example_basic_usage()
        elif choice == "2":
            example_specific_monitor()
        elif choice == "3":
            example_monitor_selection()
        elif choice == "4":
            example_region_monitoring()
        elif choice == "5":
            example_test_screenshot()
        elif choice == "0":
            print("退出")
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
