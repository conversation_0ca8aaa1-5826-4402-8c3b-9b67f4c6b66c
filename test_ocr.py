#!/usr/bin/env python3
"""
OCR功能测试脚本
用于验证tesseract和相关依赖是否正确安装
"""

import sys
import os

def test_imports():
    """测试必要的包导入"""
    print("测试包导入...")
    
    try:
        import pyautogui
        print("✓ pyautogui 导入成功")
    except ImportError as e:
        print(f"✗ pyautogui 导入失败: {e}")
        return False
    
    try:
        import pytesseract
        print("✓ pytesseract 导入成功")
    except ImportError as e:
        print(f"✗ pytesseract 导入失败: {e}")
        return False
    
    try:
        import cv2
        print("✓ opencv-python 导入成功")
    except ImportError as e:
        print(f"✗ opencv-python 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ numpy 导入成功")
    except ImportError as e:
        print(f"✗ numpy 导入失败: {e}")
        return False
    
    try:
        from PIL import Image
        print("✓ Pillow 导入成功")
    except ImportError as e:
        print(f"✗ Pillow 导入失败: {e}")
        return False
    
    return True

def test_tesseract():
    """测试tesseract OCR引擎"""
    print("\n测试Tesseract OCR引擎...")
    
    try:
        import pytesseract
        
        # 测试tesseract版本
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract版本: {version}")
        
        # 测试可用语言
        languages = pytesseract.get_languages()
        print(f"✓ 可用语言: {', '.join(languages)}")
        
        if 'chi_sim' in languages:
            print("✓ 中文简体支持已安装")
        else:
            print("⚠ 中文简体支持未安装，可能影响中文识别")
        
        return True
        
    except Exception as e:
        print(f"✗ Tesseract测试失败: {e}")
        print("请检查Tesseract是否正确安装")
        return False

def test_screen_capture():
    """测试屏幕截取功能"""
    print("\n测试屏幕截取...")
    
    try:
        import pyautogui
        
        # 获取屏幕尺寸
        screen_width, screen_height = pyautogui.size()
        print(f"✓ 屏幕尺寸: {screen_width}x{screen_height}")
        
        # 截取屏幕
        screenshot = pyautogui.screenshot()
        print(f"✓ 屏幕截取成功，图像尺寸: {screenshot.size}")
        
        return True
        
    except Exception as e:
        print(f"✗ 屏幕截取失败: {e}")
        return False

def test_ocr_functionality():
    """测试OCR识别功能"""
    print("\n测试OCR识别功能...")
    
    try:
        import pyautogui
        import pytesseract
        import cv2
        import numpy as np
        
        # 截取屏幕
        screenshot = pyautogui.screenshot()
        
        # 转换为numpy数组
        img_array = np.array(screenshot)
        
        # 转换为灰度图
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        
        # 应用阈值处理
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # OCR识别
        custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
        text = pytesseract.image_to_string(thresh, config=custom_config)
        
        if text.strip():
            print("✓ OCR识别成功")
            print(f"识别到的文字片段: {text[:100]}...")
            
            # 检查是否识别到Augment相关文字
            if 'Augment' in text or 'Agent' in text:
                print("✓ 检测到Augment Agent相关文字")
            
            return True
        else:
            print("⚠ OCR识别结果为空，可能需要调整参数")
            return False
            
    except Exception as e:
        print(f"✗ OCR识别测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== OCR功能测试 ===\n")
    
    # 测试包导入
    if not test_imports():
        print("\n请先安装必要的依赖包:")
        print("pip install -r requirements.txt")
        return
    
    # 测试tesseract
    if not test_tesseract():
        print("\n请安装Tesseract OCR引擎:")
        print("macOS: brew install tesseract")
        print("Windows: 下载安装包 https://github.com/UB-Mannheim/tesseract/wiki")
        print("Ubuntu: sudo apt install tesseract-ocr tesseract-ocr-chi-sim")
        return
    
    # 测试屏幕截取
    if not test_screen_capture():
        print("\n屏幕截取功能异常，请检查系统权限设置")
        return
    
    # 测试OCR功能
    if not test_ocr_functionality():
        print("\n OCR功能可能需要进一步配置")
        return
    
    print("\n=== 所有测试通过! ===")
    print("现在可以运行主程序: python desktop_monitor.py")

if __name__ == "__main__":
    main()
