#!/usr/bin/env python3
"""
多显示器功能测试脚本
"""

import sys
from desktop_monitor import DesktopMonitor

def test_monitor_detection():
    """测试显示器检测功能"""
    print("=== 测试显示器检测 ===")
    
    try:
        monitor = DesktopMonitor()
        
        print(f"检测到 {len(monitor.monitors)} 个显示器:")
        for i, monitor_info in enumerate(monitor.monitors):
            primary_str = " (主显示器)" if monitor_info['is_primary'] else ""
            print(f"  显示器 {i}: {monitor_info['width']}x{monitor_info['height']} "
                  f"位置({monitor_info['x']}, {monitor_info['y']}){primary_str}")
        
        return True
        
    except Exception as e:
        print(f"显示器检测失败: {e}")
        return False

def test_screenshot_all_monitors():
    """测试截取所有显示器"""
    print("\n=== 测试截取所有显示器 ===")
    
    try:
        monitor = DesktopMonitor()
        
        # 截取所有显示器
        screenshot = monitor.capture_screen()
        print(f"全屏截图尺寸: {screenshot.size}")
        
        # 保存截图（可选）
        # screenshot.save("all_monitors.png")
        # print("截图已保存为 all_monitors.png")
        
        return True
        
    except Exception as e:
        print(f"截取所有显示器失败: {e}")
        return False

def test_screenshot_individual_monitors():
    """测试截取单个显示器"""
    print("\n=== 测试截取单个显示器 ===")
    
    try:
        monitor = DesktopMonitor()
        
        for i, monitor_info in enumerate(monitor.monitors):
            print(f"截取显示器 {i}...")
            screenshot = monitor.capture_screen(monitor_index=i)
            print(f"  显示器 {i} 截图尺寸: {screenshot.size}")
            
            # 验证截图尺寸是否正确
            expected_size = (monitor_info['width'], monitor_info['height'])
            if screenshot.size == expected_size:
                print(f"  ✓ 尺寸正确")
            else:
                print(f"  ⚠ 尺寸不匹配，期望: {expected_size}, 实际: {screenshot.size}")
            
            # 保存截图（可选）
            # screenshot.save(f"monitor_{i}.png")
        
        return True
        
    except Exception as e:
        print(f"截取单个显示器失败: {e}")
        return False

def test_ocr_on_monitors():
    """测试在不同显示器上的OCR功能"""
    print("\n=== 测试OCR功能 ===")
    
    try:
        monitor = DesktopMonitor()
        
        for i, monitor_info in enumerate(monitor.monitors):
            print(f"测试显示器 {i} 的OCR功能...")
            screenshot = monitor.capture_screen(monitor_index=i)
            text = monitor.extract_text_from_image(screenshot)
            
            if text.strip():
                print(f"  ✓ OCR成功，识别到文字: {len(text)} 个字符")
                # 显示前100个字符
                preview = text[:100].replace('\n', ' ').strip()
                if preview:
                    print(f"  预览: {preview}...")
            else:
                print(f"  ⚠ OCR未识别到文字")
        
        return True
        
    except Exception as e:
        print(f"OCR测试失败: {e}")
        return False

def test_region_capture():
    """测试区域截取功能"""
    print("\n=== 测试区域截取功能 ===")
    
    try:
        monitor = DesktopMonitor()
        
        # 测试在第一个显示器上截取一个小区域
        if len(monitor.monitors) > 0:
            monitor_info = monitor.monitors[0]
            
            # 截取左上角 400x300 的区域
            region = (0, 0, 400, 300)
            print(f"截取显示器 0 的区域 {region}...")
            
            screenshot = monitor.capture_screen(region=region, monitor_index=0)
            print(f"区域截图尺寸: {screenshot.size}")
            
            # 验证尺寸
            if screenshot.size == (400, 300):
                print("✓ 区域截取成功")
            else:
                print(f"⚠ 区域截取尺寸不正确，期望: (400, 300), 实际: {screenshot.size}")
            
            return True
        else:
            print("没有可用的显示器")
            return False
        
    except Exception as e:
        print(f"区域截取测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 多显示器功能测试 ===\n")
    
    tests = [
        ("显示器检测", test_monitor_detection),
        ("截取所有显示器", test_screenshot_all_monitors),
        ("截取单个显示器", test_screenshot_individual_monitors),
        ("OCR功能", test_ocr_on_monitors),
        ("区域截取", test_region_capture)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 出错: {e}")
        
        print("-" * 50)
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！多显示器功能正常")
    else:
        print("⚠ 部分测试失败，请检查配置")
        
    print("\n提示:")
    print("- 如果OCR测试失败，请检查tesseract安装")
    print("- 如果显示器检测失败，请安装screeninfo: pip install screeninfo")
    print("- 运行主程序: python desktop_monitor.py")

if __name__ == "__main__":
    main()
