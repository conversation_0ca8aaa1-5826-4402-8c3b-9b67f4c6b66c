# Augment Agent 桌面监控器

这是一个自动化脚本，用于监控桌面上的文字内容，识别新的request_id，并在Augment Agent输入框中自动输入"继续"。

## 功能特性

- 🖥️ 实时监控桌面内容
- 🔍 OCR文字识别技术
- 🎯 智能定位Augment Agent输入框
- 📝 自动输入"继续"命令
- 💾 记录已处理的request_id，避免重复处理
- ⚙️ 可配置的监控参数

## 安装依赖

### 1. 安装Python包

```bash
pip install -r requirements.txt
```

### 2. 安装Tesseract OCR引擎

#### macOS (推荐使用Homebrew)
```bash
brew install tesseract
brew install tesseract-lang  # 可选：额外语言包
```

#### Windows
1. 下载Tesseract安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径：`C:\Program Files\Tesseract-OCR\`
3. 将安装路径添加到系统PATH环境变量

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install tesseract-ocr
sudo apt install tesseract-ocr-chi-sim  # 中文支持
```

## 使用方法

### 基本使用

```bash
python desktop_monitor.py
```

### 配置说明

编辑 `config.py` 文件来调整设置：

- `MONITOR_CONFIG['interval']`: 监控间隔（秒）
- `MONITOR_CONFIG['region']`: 监控区域，None表示全屏
- `REQUEST_ID_PATTERNS`: request_id的正则表达式模式
- `AUTO_INPUT_TEXT`: 自动输入的文字

### 自定义Tesseract路径

如果Tesseract安装在非标准路径，请在 `desktop_monitor.py` 中取消注释并修改相应行：

```python
# 例如Windows自定义路径
pytesseract.pytesseract.tesseract_cmd = r'D:\Tesseract\tesseract.exe'

# 例如macOS自定义路径
pytesseract.pytesseract.tesseract_cmd = '/usr/local/bin/tesseract'
```

## 工作原理

1. **屏幕截取**: 定期截取桌面屏幕
2. **文字识别**: 使用OCR技术提取屏幕上的文字
3. **模式匹配**: 使用正则表达式查找request_id
4. **去重检查**: 检查是否为新的request_id
5. **输入框定位**: 智能定位Augment Agent输入框
6. **自动输入**: 在输入框中输入"继续"并按回车

## 输入框识别方法

脚本使用多种方法来定位Augment Agent输入框：

1. **OCR文字识别**: 查找"Ask or instruct Augment Agent"占位符文字
2. **视觉特征检测**: 通过边缘检测识别输入框轮廓
3. **备用定位**: 根据界面布局估算输入框位置

## 安全特性

- **鼠标安全**: 将鼠标移到屏幕左上角可紧急停止程序
- **键盘中断**: 按Ctrl+C可随时停止
- **操作间隔**: 设置了操作间隔，避免过快操作
- **错误处理**: 完善的异常处理机制

## 故障排除

### OCR识别问题
- 确保Tesseract正确安装
- 检查语言包是否安装（中文支持）
- 调整OCR配置参数

### 输入框定位问题
- 确保Augment Agent界面可见
- 检查屏幕分辨率和缩放设置
- 调整监控区域设置

### 性能问题
- 增加监控间隔
- 减小监控区域
- 关闭不必要的后台程序

## 文件说明

- `desktop_monitor.py`: 主程序文件
- `config.py`: 配置文件
- `requirements.txt`: Python依赖列表
- `processed_requests.json`: 已处理的request_id记录（自动生成）
- `README.md`: 使用说明

## 注意事项

1. 首次运行前请测试OCR功能是否正常
2. 确保Augment Agent界面在屏幕上可见
3. 避免在监控期间移动或遮挡相关窗口
4. 定期检查已处理的request_id记录文件

## 许可证

MIT License
